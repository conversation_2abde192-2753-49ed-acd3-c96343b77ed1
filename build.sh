#!/usr/bin/env bash
set -o errexit


# --- Build Script for CozyWish Application ---
echo "🔍  Step 1: Installing dependencies from requirements.txt..."
pip install -r requirements.txt
echo "✅  Dependencies installed successfully."



# --- Static Files Setup ---
echo "🎨  Step 2: Collecting static files..."
python manage.py collectstatic --no-input --clear
echo "✅  Static files collected."



# --- Database Setup ---
echo "🔧  Step 3: Making migrations..."
python manage.py makemigrations
echo "✅  Migrations made."

echo "🚀  Step 4: Applying database migrations..."
python manage.py migrate
echo "✅  Database migrated successfully."

# --- Seed Data Setup (Production) ---
echo "🌱  Step 5: Seeding initial data..."
# Check if database is empty (no users)
if python manage.py shell -c "from django.contrib.auth import get_user_model; User = get_user_model(); exit(0 if User.objects.count() == 0 else 1)" 2>/dev/null; then
    echo "��  Database is empty, seeding initial data..."
    python manage.py seed_data --force-production --reset-db
    echo "✅  Initial data seeded successfully."
else
    echo "ℹ️   Database already contains data, skipping seeding."
fi



# --- URL Resolution Test ---
echo "🔍  Step 6: Testing URL resolution..."
python manage.py test_urls --url-name=home
if [ $? -eq 0 ]; then
    echo "✅  URL resolution test passed."
else
    echo "⚠️   URL resolution test failed, but continuing..."
fi



# --- Completion Message ---
echo "🎉  Build script completed successfully! All steps done. 🎉"

